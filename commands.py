import os
import webbrowser
import datetime
import wikipedia
from speech import speak

def handle_command(command):
    if "time" in command:
        now = datetime.datetime.now().strftime("%H:%M")
        speak(f"The time is {now}")

    elif "open YouTube" in command:
        speak("Opening YouTube")
        webbrowser.open("https://youtube.com")

    elif "open code" in command:
        speak("Opening VS Code")
        os.system("code")  # Make sure code is in PATH

    elif "search Wikipedia" in command:
        speak("Searching Wikipedia")
        topic = command.replace("search Wikipedia for", "")
        try:
            result = wikipedia.summary(topic, sentences=2)
            speak(result)
        except wikipedia.exceptions.DisambiguationError:
            speak("Too many results. Please be more specific.")
        except:
            speak("Couldn't find information.")

    else:
        speak("Sorry, I don't know how to do that yet.")
